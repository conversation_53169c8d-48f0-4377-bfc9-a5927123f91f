# Soniox WebSocket Keep-Alive Implementation

This document describes the WebSocket keep-alive functionality implemented for the Soniox Speech-to-Text service, following the official Soniox documentation guidelines.

## Overview

The keep-alive implementation ensures that WebSocket connections to Soniox remain active during periods of silence, such as when Voice Activity Detection (VAD) detects no speech. This is crucial for maintaining session context and preventing connection timeouts.

## Official Documentation

This implementation follows the Soniox official documentation:
- **URL**: https://soniox.com/docs/speech-to-text/core-concepts/connection-keepalive
- **Requirement**: Send `{"type": "keepalive"}` message at least once every 20 seconds during periods when no audio is being sent
- **Purpose**: Maintain connection and preserve session-level context (speaker labels, language tracking, prompts)

## Implementation Details

### Core Components

1. **Keep-alive Handler** (`keepalive_handler()`)
   - Runs as an async coroutine alongside the main message handler
   - Sends keep-alive messages every 15 seconds (configurable, default well within 20s requirement)
   - Only sends keep-alive when no audio has been sent recently
   - Automatically stops when connection is closed

2. **Audio Tracking** (`_update_last_audio_time()`)
   - Tracks when audio data is last sent to the WebSocket
   - Prevents unnecessary keep-alive messages during active audio streaming
   - Integrated with the main audio processing pipeline

3. **Lifecycle Management**
   - Keep-alive task is created when WebSocket connection is established
   - Task is properly cancelled and cleaned up when connection is closed
   - Handles both normal closure and error conditions

### Key Features

- **VAD Integration**: Works seamlessly with Voice Activity Detection to maintain connections during detected silence periods
- **Configurable Interval**: Keep-alive interval can be customized (default: 15 seconds)
- **Automatic Management**: No manual intervention required - automatically starts and stops with connection
- **Error Handling**: Robust error handling for network issues and connection problems
- **Resource Cleanup**: Proper cleanup of async tasks to prevent memory leaks

## Usage

### Basic Usage

The keep-alive functionality is automatically enabled when creating a Soniox WebSocket connection:

```python
from utils.stt.streaming import process_audio_soniox

# Create connection with default 15-second keep-alive interval
socket = await process_audio_soniox(
    stream_transcript=my_transcript_handler,
    sample_rate=16000,
    language='en',
    uid='user123'
)

# Send audio data - this automatically updates the last audio time
await socket.send(audio_data)
socket._update_last_audio_time()  # Called automatically in the main pipeline
```

### Custom Keep-alive Interval

```python
# Use custom 10-second interval
socket = await process_audio_soniox(
    stream_transcript=my_transcript_handler,
    sample_rate=16000,
    language='en',
    uid='user123',
    keepalive_interval=10  # Custom interval in seconds
)
```

### Integration with Main Pipeline

The keep-alive functionality is automatically integrated into the main transcription pipeline in `backend/routers/transcribe.py`:

```python
# Audio is sent to Soniox
await soniox_socket.send(data)
# Last audio time is automatically updated
if hasattr(soniox_socket, '_update_last_audio_time'):
    soniox_socket._update_last_audio_time()
```

## VAD Integration

The keep-alive implementation is designed to work with Voice Activity Detection (VAD):

1. **During Speech**: VAD detects speech → audio is sent → keep-alive is suppressed
2. **During Silence**: VAD detects silence → no audio sent → keep-alive maintains connection
3. **Timeout Prevention**: Connection stays alive during long silence periods until a pre-defined timeout

This ensures that:
- The WebSocket connection remains open during natural pauses in conversation
- Session context (speaker identification, language hints) is preserved
- No unnecessary reconnections are required

## Configuration

### Environment Variables

- `SONIOX_API_KEY`: Required API key for Soniox service

### Parameters

- `keepalive_interval`: Interval in seconds between keep-alive messages (default: 15)
- Must be less than 20 seconds to comply with Soniox requirements

## Error Handling

The implementation includes comprehensive error handling:

1. **Connection Errors**: Gracefully handles WebSocket connection issues
2. **Send Errors**: Catches and logs errors when sending keep-alive messages
3. **Cancellation**: Properly handles task cancellation during shutdown
4. **Resource Cleanup**: Ensures async tasks are cleaned up to prevent memory leaks

## Testing

### Unit Tests

Run the unit tests to verify keep-alive functionality:

```bash
cd backend
python -m pytest tests/test_soniox_keepalive.py -v
```

### Integration Tests

Run integration tests to verify real-world scenarios:

```bash
cd backend
python tests/test_soniox_integration.py
```

### Manual Testing

To manually test the keep-alive functionality:

1. Set up a Soniox API key in your environment
2. Start a transcription session
3. Send audio for a few seconds
4. Stop sending audio and observe keep-alive messages in logs
5. Resume sending audio and verify keep-alive stops

## Monitoring and Debugging

### Log Messages

The implementation provides detailed logging:

- `"Soniox keepalive handler started with Xs interval"`
- `"Sent Soniox keepalive message after X.Xs of silence"`
- `"Soniox keepalive handler stopped"`

### Debugging Tips

1. Check that `SONIOX_API_KEY` is set correctly
2. Verify keep-alive messages appear in logs during silence periods
3. Ensure `_update_last_audio_time()` is called when sending audio
4. Monitor WebSocket connection status

## Best Practices

1. **Use Default Interval**: The default 15-second interval is recommended for most use cases
2. **Monitor Connection Health**: Watch for keep-alive messages in logs to ensure functionality
3. **Proper Cleanup**: Always ensure WebSocket connections are properly closed
4. **Error Handling**: Implement proper error handling in your transcript callback functions

## Compliance

This implementation fully complies with Soniox's official keep-alive requirements:

- ✅ Sends keep-alive messages at least every 20 seconds during silence
- ✅ Uses correct message format: `{"type": "keepalive"}`
- ✅ Maintains session context during silence periods
- ✅ Integrates with VAD for optimal performance
- ✅ Handles connection lifecycle properly

## Future Enhancements

Potential improvements for future versions:

1. **Adaptive Intervals**: Dynamically adjust keep-alive interval based on network conditions
2. **Health Monitoring**: Add connection health metrics and monitoring
3. **Retry Logic**: Implement automatic reconnection with exponential backoff
4. **Performance Metrics**: Track keep-alive effectiveness and connection stability
